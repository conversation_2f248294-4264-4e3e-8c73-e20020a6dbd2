// Centralized OpenAPI schemas that can be imported by route files and openapi-spec.ts
import { OpenAPIV3 } from 'openapi-types';

export const commonSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // General Error Schema
  ErrorResponse: {
    type: 'object',
    properties: {
      statusCode: { type: 'integer', example: 400 },
      error: { type: 'string', example: 'Bad Request' },
      message: { type: 'string', example: 'Invalid input' },
    },
    required: ['statusCode', 'error', 'message'],
  },
};

export const authSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // Admin Login Schemas
  AdminLoginPayload: {
    type: 'object',
    properties: {
      username: { type: 'string', example: 'admin' },
      password: { type: 'string', format: 'password', example: 'securepassword123' },
    },
    required: ['username', 'password'],
  },
  AdminLoginSuccessResponse: {
    type: 'object',
    properties: {
      message: { type: 'string', example: 'Admin login successful' },
      token: { type: 'string', description: "JWT token (also set as httpOnly cookie 'admin_token')" }
    },
  },

  // User Profile Schema
  UserProfile: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cuid123' },
      email: { type: 'string', format: 'email', example: '<EMAIL>' },
      name: { type: 'string', nullable: true, example: 'John Doe' },
      monthlyEmailLimit: { type: 'integer', example: 50 },
      planType: { type: 'string', enum: ['free', 'pro', 'enterprise'], example: 'free' },
      currentMonthEmails: { type: 'integer', example: 25 },
      verified: { type: 'boolean', example: true }
    },
    required: ['id', 'email', 'monthlyEmailLimit', 'planType', 'currentMonthEmails', 'verified']
  },
};

export const domainSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Domain Schemas ---
  Domain: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid', readOnly: true },
      domain: { type: 'string', example: 'example.com' },
      // userId: { type: 'string', format: 'uuid' },
      apiKey: { type: 'string', readOnly: true, description: 'API key for this domain' },
      dkimPublicKey: { type: 'string', readOnly: true, nullable: true },
      dkimPrivateKey: { type: 'string', readOnly: true, nullable: true },
      dkimSelector: { type: 'string', readOnly: true, nullable: true },
      isVerified: { type: 'boolean', default: false, readOnly: true },
      verificationToken: { type: 'string', readOnly: true, nullable: true },
      createdAt: { type: 'string', format: 'date-time', readOnly: true },
      updatedAt: { type: 'string', format: 'date-time', readOnly: true },
    },
    required: ['domain'],
  },
  CreateDomainPayload: {
    type: 'object',
    properties: {
      domain: { type: 'string', example: 'mydomain.com' },
    },
    required: ['domain'],
  },
  DomainStatus: {
    type: 'object',
    properties: {
      domain: { type: 'string', example: 'example.com' },
      isVerified: { type: 'boolean' },
      dnsRecords: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: { type: 'string', example: 'TXT' },
            name: { type: 'string', example: 'example.com or _dkim.example.com' },
            value: { type: 'string', example: 'verification-token or dkim-public-key' },
            status: { type: 'string', enum: ['verified', 'pending', 'failed'], example: 'pending' },
          },
        },
      },
    },
  },
};

export const aliasSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Alias Schemas ---
  Alias: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid', readOnly: true },
      domainId: { type: 'string', format: 'uuid' },
      localPart: { type: 'string', example: 'support' }, // e.g., 'support' for <EMAIL>
      destinationEmail: { type: 'string', format: 'email', example: '<EMAIL>' },
      createdAt: { type: 'string', format: 'date-time', readOnly: true },
      updatedAt: { type: 'string', format: 'date-time', readOnly: true },
    },
    required: ['domainId', 'localPart', 'destinationEmail'],
  },
  CreateAliasPayload: {
    type: 'object',
    properties: {
      localPart: { type: 'string', example: 'info' },
      destinationEmail: { type: 'string', format: 'email', example: '<EMAIL>' },
    },
    required: ['localPart', 'destinationEmail'],
  },
  UpdateAliasPayload: {
    type: 'object',
    properties: {
      destinationEmail: { type: 'string', format: 'email', example: '<EMAIL>' },
    },
    required: ['destinationEmail'],
  },
};

export const webhookSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Webhook Schemas ---
  WebhookLog: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
      domainId: { type: 'string', format: 'uuid' },
      receivedAt: { type: 'string', format: 'date-time' },
      sender: { type: 'string', format: 'email' },
      recipient: { type: 'string', format: 'email' },
      subject: { type: 'string', nullable: true },
      status: { type: 'string', enum: ['received', 'processed', 'failed'] },
      payload: { type: 'object', description: 'Raw email payload or structured data' },
      errorMessage: { type: 'string', nullable: true },
    },
  },
};

export const adminSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Admin Schemas ---
  AdminDomainView: {
    allOf: [
      { $ref: 'Domain#' },
      {
        type: 'object',
        properties: {
          user: { // Basic user info, expand as needed
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              email: { type: 'string', format: 'email' }, // Example user identifier
            },
          },
        },
      },
    ],
  },
  DeliveryStatistic: {
    type: 'object',
    properties: {
      date: { type: 'string', format: 'date' },
      totalReceived: { type: 'integer' },
      totalDelivered: { type: 'integer' },
      totalFailed: { type: 'integer' },
    },
  },
};

// Common response schemas
export const responseSchemas: Record<string, OpenAPIV3.ResponseObject> = {
  BadRequest: {
    description: 'Invalid request payload or parameters.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  Unauthorized: {
    description: 'Authentication failed or missing API key for user endpoint.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  Forbidden: {
    description: 'User is authenticated but does not have permission to access the resource.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  NotFound: {
    description: 'The requested resource was not found.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  UnauthorizedAdmin: {
    description: 'Authentication failed or missing credentials for admin endpoint.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  ForbiddenAdmin: {
    description: 'Admin is authenticated but does not have permission for this specific admin action.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  }
};

// Combine all schemas for easy import
export const allSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  ...commonSchemas,
  ...authSchemas,
  ...domainSchemas,
  ...aliasSchemas,
  ...webhookSchemas,
  ...adminSchemas, // Include for route functionality, filter out in Swagger docs
};

// Public schemas for API documentation (excludes admin schemas)
export const publicSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  ...commonSchemas,
  ...authSchemas,
  ...domainSchemas,
  ...aliasSchemas,
  ...webhookSchemas,
};

export const allResponses = responseSchemas;
