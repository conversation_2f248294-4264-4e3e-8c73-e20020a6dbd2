import { OpenAPIV3 } from 'openapi-types';
import { allSchemas, allResponses } from './schemas/openapi-schemas.js';

export const openApiSpecification: OpenAPIV3.Document = {
  openapi: '3.0.0',
  info: {
    title: 'EmailConnect.eu API',
    version: '1.0.0',
    description: `
# EmailConnect.eu API Documentation

Welcome to the EmailConnect.eu API! This API allows you to programmatically manage your email domains, aliases, and webhooks.

## Authentication

All API endpoints require authentication using your API key. Include your API key in the request header:

\`\`\`
X-API-KEY: your-api-key-here
\`\`\`

You can find your API key in your [account settings](${process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'}/settings).

## Rate Limits

API requests are rate-limited to ensure fair usage:
- **Free Plan**: 100 requests per hour
- **Pro Plan**: 1,000 requests per hour
- **Enterprise Plan**: 10,000 requests per hour

## Support

Need help? Contact us at [<EMAIL>](mailto:<EMAIL>) or visit our [help center](${process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'}/docs).
    `,
    contact: {
      name: 'EmailConnect.eu Support',
      email: '<EMAIL>',
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'
    },
    license: {
      name: 'Terms of Service',
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/terms-of-service' : 'http://localhost:3000/terms-of-service'
    }
  },
  servers: [
    {
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000',
      description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
    },
  ],
  components: {
    securitySchemes: {
      userApiKey: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-KEY',
        description: 'API Key for user authentication. Find your API key in your account settings.',
      },
    },
    schemas: allSchemas,
    responses: allResponses,
  },
  tags: [
    {
      name: 'User Domains',
      description: 'Manage your email domains. Add domains, verify ownership, and configure email routing.',
      externalDocs: {
        description: 'Domain setup guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#domains' : 'http://localhost:3000/docs#domains'
      }
    },
    {
      name: 'User Aliases',
      description: 'Create and manage email aliases for your verified domains. Route specific email addresses to different webhooks.',
      externalDocs: {
        description: 'Alias configuration guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#aliases' : 'http://localhost:3000/docs#aliases'
      }
    },
    {
      name: 'User Webhooks',
      description: 'Configure webhook endpoints to receive email data. Set up multiple webhooks per domain for different processing needs.',
      externalDocs: {
        description: 'Webhook integration guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#webhooks' : 'http://localhost:3000/docs#webhooks'
      }
    },
  ],
  paths: {
    // Only user-facing paths are included
    // Admin, internal webhook processing, billing, dashboard, template, and log routes are excluded
    // These paths are auto-generated by Fastify from the schema definitions in the respective route files
  },
};

// Helper function to merge paths from route files
export function mergeRoutePaths(additionalPaths: Record<string, OpenAPIV3.PathItemObject>) {
  return {
    ...openApiSpecification,
    paths: {
      ...openApiSpecification.paths,
      ...additionalPaths,
    },
  };
}
